import uuid

def flatten_results(results, embeddings):
    """把Extraction结果和embedding整合成Milvus可插入的dict"""
    processed = []
    for result, embedding in zip(results, embeddings):
        data_entry = {
            "id": result.document_id or str(uuid.uuid4()),
            "document_text": result.text,
            "embedding": embedding,
            "genre": "unknown",
            "primary_genre": "unknown",
            "secondary_genre": "unknown",
            "character_role": "unknown",
            "character_type": "unknown",
            "theme_type": "unknown",
            "theme_setting": "unknown",
        }
        for extraction in result.extractions:
            if extraction.extraction_class == "genre":
                attrs = extraction.attributes or {}
                data_entry["genre"] = extraction.extraction_text
                data_entry["primary_genre"] = attrs.get("primary_genre", "unknown")
                data_entry["secondary_genre"] = attrs.get("secondary_genre", "unknown")
            elif extraction.extraction_class == "character" and data_entry["character_role"] == "unknown":
                attrs = extraction.attributes or {}
                data_entry["character_role"] = attrs.get("role", "unknown")
                data_entry["character_type"] = attrs.get("type", "unknown")
            elif extraction.extraction_class == "theme" and data_entry["theme_type"] == "unknown":
                attrs = extraction.attributes or {}
                data_entry["theme_type"] = attrs.get("theme_type", "unknown")
                data_entry["theme_setting"] = attrs.get("setting", "unknown")
        processed.append(data_entry)
    return processed
