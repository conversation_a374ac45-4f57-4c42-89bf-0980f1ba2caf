def run_searches(client, collection_name, genai_client, embedding_model, embedding_dim):
    """演示查询"""
    print("Loading collection into memory...")
    client.load_collection(collection_name=collection_name)

    print("\n1. Search thriller movies:")
    results = client.query(
        collection_name=collection_name,
        filter='secondary_genre == "thriller"',
        output_fields=["document_text", "genre", "primary_genre", "secondary_genre"],
        limit=5,
    )
    for r in results:
        print(f"- {r['document_text'][:100]}... {r['genre']}")

    # 可以在这里继续加入语义搜索示例
