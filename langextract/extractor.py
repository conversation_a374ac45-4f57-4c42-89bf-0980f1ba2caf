import langextract as lx

def extract_tags(documents, prompt, examples):
    """调用LangExtract批量提取标签"""
    results = []
    for doc in documents:
        result = lx.extract(
            ext_or_documents=doc,
            prompt_description=prompt,
            examples=examples,
            model_id="gemini-2.0-flash",
        )
        results.append(result)
        print(f"Extracted from: {doc[:50]}...")
    return results
