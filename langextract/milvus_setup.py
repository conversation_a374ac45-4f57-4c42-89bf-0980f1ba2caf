from pymilvus import DataType

def init_milvus_collection(client, collection_name, embedding_dim):
    """创建或重建Milvus collection并返回"""
    if client.has_collection(collection_name=collection_name):
        client.drop_collection(collection_name=collection_name)
        print(f"Dropped existing collection: {collection_name}")

    schema = client.create_schema(
        auto_id=False,
        enable_dynamic_field=True,
        description="Document extraction results and vector storage",
    )
    schema.add_field("id", datatype=DataType.VARCHAR, max_length=100, is_primary=True)
    schema.add_field("document_text", datatype=DataType.VARCHAR, max_length=10000)
    schema.add_field("embedding", datatype=DataType.FLOAT_VECTOR, dim=embedding_dim)

    client.create_collection(collection_name=collection_name, schema=schema)
    print(f"Collection '{collection_name}' created successfully")

    index_params = client.prepare_index_params()
    index_params.add_index(field_name="embedding", index_type="AUTOINDEX", metric_type="COSINE")
    client.create_index(collection_name=collection_name, index_params=index_params)
    print("Vector index created successfully")
