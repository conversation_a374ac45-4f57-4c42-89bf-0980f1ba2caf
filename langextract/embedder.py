from google.genai.types import EmbedContentConfig

def embed_texts(genai_client, model, texts, dim):
    """批量向量化文本"""
    embeddings = []
    for t in texts:
        resp = genai_client.models.embed_content(
            model=model,
            contents=[t],
            config=EmbedContentConfig(
                task_type="RETRIEVAL_DOCUMENT",
                output_dimensionality=dim,
            ),
        )
        embeddings.append(resp.embeddings[0].values)
        print(f"Generated vector for: {t[:30]}...")
    return embeddings
